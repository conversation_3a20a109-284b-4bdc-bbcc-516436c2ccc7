import { useEffect, useState } from "react";
import Video from "~/components/Video";
import * as videoService from "~/service/videoService";

function Home() {
  const [listVideos, setListVideos] = useState([]);
  const [pages, setPages] = useState(1);
  const [mute, setMute] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const [prevVolume, setPrevVolume] = useState(volume);

  useEffect(() => {
    // const fetchApi = async () => {
    //   const res = await videoService.getVideos();
    //   console.log(res);
    // };
    // fetchApi();

    videoService
      .getVideos({ type: "for-you", page: pages })
      .then((data) => {
        setListVideos((prevVideos) => [...prevVideos, ...data]);
      })
      .catch((error) => console.log(error));
  }, [pages]);

  const handleSliderVolume = (e) => {
    setVolume(e.target.value / 100);
  };

  const toggleVolume = () => {
    if (mute) {
      setVolume(prevVolume);
      setMute(false);
    } else {
      setPrevVolume(volume);
      setVolume(0);
      setMute(true);
    }
  };
  const handleScroll = () => {
    if (window.scrollY + window.innerHeight >= document.body.offsetHeight) {
      setPages((pages) => pages + 1);
    }
  };
  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  });

  return (
    <div className="wrapper">
      {listVideos.map((video) => {
        return (
          <Video
            key={video.id}
            data={video}
            volume={volume}
            mute={mute}
            handleSliderVolume={handleSliderVolume}
            toggleVolume={toggleVolume}
          />
        );
      })}
    </div>
  );
}

export default Home;
